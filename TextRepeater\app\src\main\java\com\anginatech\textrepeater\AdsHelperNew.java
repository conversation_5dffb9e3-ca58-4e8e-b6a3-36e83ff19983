package com.anginatech.textrepeater;

import android.app.Activity;
import android.content.Context;
import android.content.SharedPreferences;
import android.os.Handler;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.android.volley.Request;
import com.android.volley.RequestQueue;
import com.android.volley.toolbox.JsonObjectRequest;
import com.android.volley.toolbox.Volley;
import com.google.android.gms.ads.AdError;
import com.google.android.gms.ads.AdListener;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdSize;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.FullScreenContentCallback;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.interstitial.InterstitialAd;
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback;

import org.json.JSONObject;

/**
 * Enhanced AdsHelper with seamless background ad management
 * - No ad break periods or cooldowns
 * - Background preloading for instant display
 * - No visible loading indicators
 * - Continuous ad refresh
 */
public class AdsHelper {
    private static final String TAG = "AdsHelper";
    
    // Ad configuration
    public static String admob_banner = "";
    public static String admob_interstitial = "";
    public static String admob_app_open = "";
    public static boolean isAds = false;
    
    // Ad instances
    private static InterstitialAd mInterstitialAd;
    private static InterstitialAd preloadedInterstitialAd;
    
    // Background management
    private static Handler handler;
    private static boolean isPreloadingInterstitial = false;
    
    /**
     * Initialize ads system with background preloading
     */
    public static void initializeAds(Context context) {
        if (handler == null) {
            handler = new Handler(android.os.Looper.getMainLooper());
        }
        
        // Load configuration and start background preloading
        loadAdsConfigurationAsync(context);
    }
    
    /**
     * Load ads configuration asynchronously without blocking UI
     */
    private static void loadAdsConfigurationAsync(Context context) {
        // Load from cache first for immediate availability
        loadAdConfigFromPrefs(context);
        
        // Update from network in background
        new Thread(() -> {
            try {
                loadAdsConfiguration(context);
            } catch (Exception e) {
                Log.e(TAG, "Error loading ads configuration: " + e.getMessage());
            }
        }).start();
    }
    
    /**
     * Load ads configuration from API
     */
    private static void loadAdsConfiguration(Context context) {
        ApiClient apiClient = ApiClient.getInstance(context);
        apiClient.fetchAdMobConfig(new ApiClient.AdMobConfigCallback() {
            @Override
            public void onSuccess(JSONObject config) {
                try {
                    admob_banner = config.optString("banner_id", "");
                    admob_interstitial = config.optString("interstitial_id", "");
                    admob_app_open = config.optString("app_open_id", "");
                    isAds = config.optInt("is_active", 1) == 1;
                    
                    Log.d(TAG, "Ad configuration loaded - Ads enabled: " + isAds);
                    
                    // Save to preferences
                    saveAdConfigToPrefs(context, config);
                    
                    // Start background preloading if ads are enabled
                    if (isAds) {
                        startBackgroundAdPreloading(context);
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error parsing ad configuration: " + e.getMessage());
                }
            }
            
            @Override
            public void onError(String error) {
                Log.e(TAG, "Error loading ad configuration: " + error);
            }
        });
    }
    
    /**
     * Save ad configuration to preferences
     */
    private static void saveAdConfigToPrefs(Context context, JSONObject config) {
        SharedPreferences prefs = context.getSharedPreferences(Config.PREFS_ADMOB_CONFIG, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        
        editor.putString(Config.PREF_BANNER_AD_ID, config.optString("banner_id", ""));
        editor.putString(Config.PREF_INTERSTITIAL_AD_ID, config.optString("interstitial_id", ""));
        editor.putString(Config.PREF_APP_OPEN_AD_ID, config.optString("app_open_id", ""));
        editor.putBoolean(Config.PREF_ADMOB_ENABLED, config.optInt("is_active", 1) == 1);
        editor.apply();
    }
    
    /**
     * Load ad configuration from preferences
     */
    private static void loadAdConfigFromPrefs(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(Config.PREFS_ADMOB_CONFIG, Context.MODE_PRIVATE);
        
        admob_banner = prefs.getString(Config.PREF_BANNER_AD_ID, "");
        admob_interstitial = prefs.getString(Config.PREF_INTERSTITIAL_AD_ID, "");
        admob_app_open = prefs.getString(Config.PREF_APP_OPEN_AD_ID, "");
        isAds = prefs.getBoolean(Config.PREF_ADMOB_ENABLED, Config.DEFAULT_ADMOB_ENABLED);
        
        // Start background preloading if ads are enabled
        if (isAds) {
            startBackgroundAdPreloading(context);
        }
    }
    
    /**
     * Start background ad preloading for seamless experience
     */
    private static void startBackgroundAdPreloading(Context context) {
        if (!isAds || admob_interstitial.isEmpty()) return;
        
        new Thread(() -> {
            try {
                Thread.sleep(500); // Small delay for smooth startup
                loadInterstitialAdSilently(context);
            } catch (Exception e) {
                Log.e(TAG, "Error in background preloading: " + e.getMessage());
            }
        }).start();
    }
    
    /**
     * Check if interstitial ad is ready
     */
    public static boolean isInterstitialAdLoaded() {
        return mInterstitialAd != null;
    }
    
    /**
     * Show interstitial ad with seamless experience
     */
    public static void showInterstitialAd(Activity activity, FullScreenContentCallback userCallback) {
        if (mInterstitialAd != null) {
            Log.d(TAG, "Showing interstitial ad");
            
            FullScreenContentCallback wrapperCallback = new FullScreenContentCallback() {
                @Override
                public void onAdDismissedFullScreenContent() {
                    Log.d(TAG, "Interstitial ad dismissed");
                    mInterstitialAd = null;
                    
                    if (userCallback != null) {
                        userCallback.onAdDismissedFullScreenContent();
                    }
                    
                    // Immediately start preloading next ad
                    startBackgroundAdPreloading(activity);
                }
                
                @Override
                public void onAdFailedToShowFullScreenContent(@NonNull AdError adError) {
                    Log.e(TAG, "Interstitial ad failed to show: " + adError.getMessage());
                    mInterstitialAd = null;
                    
                    if (userCallback != null) {
                        userCallback.onAdFailedToShowFullScreenContent(adError);
                    }
                    
                    // Start preloading next ad
                    startBackgroundAdPreloading(activity);
                }
                
                @Override
                public void onAdShowedFullScreenContent() {
                    if (userCallback != null) {
                        userCallback.onAdShowedFullScreenContent();
                    }
                }
            };
            
            mInterstitialAd.setFullScreenContentCallback(wrapperCallback);
            mInterstitialAd.show(activity);
        } else {
            Log.d(TAG, "Interstitial ad not ready");
            if (userCallback != null) {
                userCallback.onAdFailedToShowFullScreenContent(null);
            }
            // Try to load ad for next time
            startBackgroundAdPreloading(activity);
        }
    }
    
    public static void showInterstitialAd(Activity activity) {
        showInterstitialAd(activity, null);
    }
    
    /**
     * Load interstitial ad silently in background
     */
    private static void loadInterstitialAdSilently(Context context) {
        if (!isAds || admob_interstitial.isEmpty() || isPreloadingInterstitial) {
            return;
        }
        
        isPreloadingInterstitial = true;
        
        new Thread(() -> {
            try {
                AdRequest adRequest = new AdRequest.Builder().build();
                
                handler.post(() -> {
                    InterstitialAd.load(context, admob_interstitial, adRequest,
                        new InterstitialAdLoadCallback() {
                            @Override
                            public void onAdLoaded(@NonNull InterstitialAd interstitialAd) {
                                mInterstitialAd = interstitialAd;
                                isPreloadingInterstitial = false;
                                Log.d(TAG, "Interstitial ad preloaded successfully");
                            }
                            
                            @Override
                            public void onAdFailedToLoad(@NonNull LoadAdError loadAdError) {
                                isPreloadingInterstitial = false;
                                Log.e(TAG, "Interstitial ad preload failed: " + loadAdError.getMessage());
                                
                                // Retry after delay
                                handler.postDelayed(() -> {
                                    if (context instanceof Activity && !((Activity) context).isFinishing()) {
                                        loadInterstitialAdSilently(context);
                                    }
                                }, 5000);
                            }
                        });
                });
            } catch (Exception e) {
                isPreloadingInterstitial = false;
                Log.e(TAG, "Error in silent ad loading: " + e.getMessage());
            }
        }).start();
    }
    
    /**
     * Load banner ad without restrictions
     */
    public static void loadBannerAd(Activity activity, ViewGroup adContainer) {
        if (!isAds || adContainer == null || admob_banner.isEmpty()) {
            if (adContainer != null) {
                adContainer.removeAllViews();
                adContainer.setVisibility(View.GONE);
            }
            return;
        }
        
        loadBannerAdAsync(activity, adContainer);
    }
    
    /**
     * Load banner ad asynchronously without restrictions
     */
    private static void loadBannerAdAsync(Activity activity, ViewGroup adContainer) {
        try {
            AdView adView = new AdView(activity);
            adView.setAdSize(AdSize.BANNER);
            adView.setAdUnitId(admob_banner);
            
            adView.setAdListener(new AdListener() {
                @Override
                public void onAdClicked() {
                    Log.d(TAG, "Banner ad clicked");
                    // Track click without restrictions
                    trackAdClick(activity);
                }
                
                @Override
                public void onAdLoaded() {
                    Log.d(TAG, "Banner ad loaded successfully");
                }
                
                @Override
                public void onAdFailedToLoad(LoadAdError loadAdError) {
                    Log.e(TAG, "Banner ad failed to load: " + loadAdError.getMessage());
                    // Retry after delay
                    handler.postDelayed(() -> {
                        if (!activity.isFinishing()) {
                            loadBannerAd(activity, adContainer);
                        }
                    }, 3000);
                }
            });
            
            adContainer.removeAllViews();
            adContainer.addView(adView);
            adContainer.setVisibility(View.VISIBLE);
            
            AdRequest adRequest = new AdRequest.Builder().build();
            adView.loadAd(adRequest);
            
        } catch (Exception e) {
            Log.e(TAG, "Error loading banner ad: " + e.getMessage());
        }
    }
    
    /**
     * Track ad click events
     */
    private static void trackAdClick(Context context) {
        new Thread(() -> {
            try {
                RequestQueue queue = Volley.newRequestQueue(context);
                JSONObject jsonBody = new JSONObject();
                jsonBody.put("event", "ad_click");
                jsonBody.put("timestamp", System.currentTimeMillis());
                jsonBody.put("ad_type", "banner");
                
                String apiUrl = Config.buildApiUrl(Config.ENDPOINT_ADS_TRACK);
                JsonObjectRequest request = new JsonObjectRequest(
                    Request.Method.POST, apiUrl, jsonBody,
                    response -> Log.d(TAG, "Ad click tracked successfully"),
                    error -> Log.w(TAG, "Ad click tracking failed: " + error.toString())
                );
                
                queue.add(request);
            } catch (Exception e) {
                Log.e(TAG, "Error tracking ad click: " + e.getMessage());
            }
        }).start();
    }
    
    /**
     * Clean up ads when disabled
     */
    public static void cleanupAds() {
        if (mInterstitialAd != null) {
            mInterstitialAd = null;
        }
        if (preloadedInterstitialAd != null) {
            preloadedInterstitialAd = null;
        }
        isPreloadingInterstitial = false;
        Log.d(TAG, "Ads cleaned up");
    }
}
