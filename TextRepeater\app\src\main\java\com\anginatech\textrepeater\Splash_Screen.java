package com.anginatech.textrepeater;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import org.json.JSONObject;

public class Splash_Screen extends AppCompatActivity {

    private static final String TAG = "Splash_Screen";
    private static final int SPLASH_SCREEN_TIMEOUT = 1000;

    private boolean isAdLoaded = false;
    private boolean isConfigLoaded = false;
    private boolean hasNavigated = false;
    private boolean isAdShowing = false;
    private Handler splashHandler;
    private Runnable navigationRunnable;

    // UI elements for enhanced loading screen
    private LinearLayout loadingContainer;
    private ProgressBar loadingProgressBar;
    private TextView loadingText;
    private TextView loadingSubText;
    private TextView loadingPercentage;
    private View pulsingDot;

    // Loading state tracking
    private long loadingStartTime;
    private int currentLoadingStep = 0;
    private final String[] loadingMessages = {
        "Initializing...",
        "Loading configuration...",
        "Preparing ads...",
        "Almost ready..."
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_splash_screen);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        // Apply night mode setting
        SharedPreferences sharedPreferences = getSharedPreferences(Config.PREFS_NIGHT_MODE, MODE_PRIVATE);
        boolean isNightMode = sharedPreferences.getBoolean("nightMode", false);

        if (isNightMode) {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES);
        } else {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);
        }

        // Initialize UI elements
        initializeLoadingScreen();

        // Initialize splash handler
        splashHandler = new Handler();
        loadingStartTime = System.currentTimeMillis();

        // Setup navigation runnable
        navigationRunnable = this::navigateToNextActivity;

        // Start with initial loading message
        updateLoadingMessage(0);

        // Start loading configuration and ads
        loadAppConfiguration();

        // Set maximum timeout for splash screen (using Config value)
        splashHandler.postDelayed(() -> {
            if (!hasNavigated && !isAdShowing) {
                Log.w(TAG, "Maximum splash timeout reached, navigating anyway");
                hideLoadingScreen();
                navigateToNextActivity();
            }
        }, Config.MAX_SPLASH_TIMEOUT);
    }

    /**
     * Initialize loading screen UI elements
     */
    private void initializeLoadingScreen() {
        try {
            loadingContainer = findViewById(R.id.loadingContainer);
            loadingProgressBar = findViewById(R.id.loadingProgressBar);
            loadingText = findViewById(R.id.loadingText);
            loadingSubText = findViewById(R.id.loadingSubText);
            loadingPercentage = findViewById(R.id.loadingPercentage);
            pulsingDot = findViewById(R.id.pulsingDot);
            
            // Start pulsing animation for the dot
            if (pulsingDot != null) {
                Animation pulseAnimation = AnimationUtils.loadAnimation(this, R.anim.pulse_animation);
                pulsingDot.startAnimation(pulseAnimation);
            }
        } catch (Exception e) {
            // Handle gracefully if UI elements are not found
            Log.w(TAG, "Loading screen UI elements not found: " + e.getMessage());
        }
    }

    /**
     * Update loading message with step progression
     */
    private void updateLoadingMessage(int step) {
        if (step < loadingMessages.length) {
            currentLoadingStep = step;
            showLoadingScreen(loadingMessages[step], "Please wait while we prepare your experience");
            
            // Update progress percentage if available
            if (loadingPercentage != null) {
                int percentage = (step + 1) * 100 / loadingMessages.length;
                loadingPercentage.setText(percentage + "%");
                loadingPercentage.setVisibility(View.VISIBLE);
            }
        }
    }

    /**
     * Show loading screen with custom message
     */
    private void showLoadingScreen(String message, String subMessage) {
        try {
            if (loadingContainer != null) {
                loadingContainer.setVisibility(View.VISIBLE);
            }
            if (loadingText != null) {
                loadingText.setText(message);
            }
            if (loadingSubText != null) {
                loadingSubText.setText(subMessage);
            }
        } catch (Exception e) {
            Log.w(TAG, "Error showing loading screen: " + e.getMessage());
        }
    }

    /**
     * Hide loading screen
     */
    private void hideLoadingScreen() {
        try {
            if (loadingContainer != null) {
                loadingContainer.setVisibility(View.GONE);
            }
        } catch (Exception e) {
            Log.w(TAG, "Error hiding loading screen: " + e.getMessage());
        }
    }

    /**
     * Load app configuration from API
     */
    private void loadAppConfiguration() {
        Log.d(TAG, "Loading app configuration...");
        updateLoadingMessage(1); // "Loading configuration..."

        ApiClient apiClient = ApiClient.getInstance(this);

        // Check maintenance status first
        apiClient.checkMaintenanceStatus((isMaintenanceMode, message) -> {
            if (isMaintenanceMode) {
                showMaintenanceDialog(message);
                return;
            }

            // Load AdMob configuration
            loadAdMobConfiguration();
        });
    }

    /**
     * Load AdMob configuration
     */
    private void loadAdMobConfiguration() {
        Log.d(TAG, "Loading AdMob configuration...");
        updateLoadingMessage(2); // "Preparing ads..."

        ApiClient apiClient = ApiClient.getInstance(this);
        apiClient.fetchAdMobConfig(new ApiClient.AdMobConfigCallback() {
            @Override
            public void onSuccess(JSONObject config) {
                Log.d(TAG, "AdMob config loaded successfully");
                isConfigLoaded = true;

                boolean adsEnabled = config.optInt("is_active", 1) == 1;
                String appOpenAdId = config.optString("app_open_id", "");

                if (adsEnabled && !appOpenAdId.isEmpty()) {
                    Log.d(TAG, "App Open Ad enabled, attempting to show ad");
                    updateLoadingMessage(3); // "Almost ready..."
                    showAppOpenAd();
                } else {
                    Log.d(TAG, "App Open Ad disabled or no ad unit ID");
                    isAdLoaded = true;
                    checkAndNavigate();
                }
            }

            @Override
            public void onError(String error) {
                Log.e(TAG, "Error loading AdMob config: " + error);
                isConfigLoaded = true;
                isAdLoaded = true; // Skip ad loading on error
                checkAndNavigate();
            }
        });
    }

    /**
     * Show app open ad seamlessly without loading indicators
     */
    private void showAppOpenAd() {
        MyApplication app = (MyApplication) getApplication();
        AppOpenAdManager adManager = app.getAppOpenAdManager();

        if (adManager != null) {
            Log.d(TAG, "Attempting to show App Open Ad seamlessly");

            // Try to show the ad with enhanced callback handling
            adManager.showAdIfAvailable(this,
                new AppOpenAdManager.OnShowAdCompleteListener() {
                    @Override
                    public void onShowAdComplete() {
                        Log.d(TAG, "App Open Ad show completed - user dismissed the ad");

                        // Mark ad as completed and hide loading screen
                        hideLoadingScreen();
                        isAdLoaded = true;
                        isAdShowing = false;

                        // Now navigate to next activity
                        checkAndNavigate();
                    }
                },
                new AppOpenAdManager.OnAdLoadingListener() {
                    @Override
                    public void onAdLoadingStarted() {
                        Log.d(TAG, "App Open Ad loading started");
                        runOnUiThread(() -> {
                            showLoadingScreen("Loading ads...", "Preparing your ad experience");
                            updateLoadingMessage(3); // "Almost ready..."
                        });
                    }

                    @Override
                    public void onAdLoadingCompleted(boolean success) {
                        Log.d(TAG, "App Open Ad loading completed: " + success);
                        if (success) {
                            runOnUiThread(() -> {
                                updateLoadingMessage(3); // "Almost ready..."
                                showLoadingScreen("Ad ready!", "Tap to continue when ad appears");
                            });
                        } else {
                            // Ad loading failed - DO NOT proceed automatically
                            // Keep trying to load the ad or show error message
                            Log.w(TAG, "App Open Ad loading failed, retrying...");
                            runOnUiThread(() -> {
                                showLoadingScreen("Ad loading failed", "Retrying... Please wait");
                            });

                            // Reset loading state and retry after a delay
                            splashHandler.postDelayed(() -> {
                                Log.d(TAG, "Retrying App Open Ad loading...");
                                MyApplication app = (MyApplication) getApplication();
                                AppOpenAdManager adManager = app.getAppOpenAdManager();
                                if (adManager != null) {
                                    adManager.resetLoadingState();
                                }
                                showAppOpenAd();
                            }, 3000); // Retry after 3 seconds
                        }
                    }

                    @Override
                    public void onAdShown() {
                        Log.d(TAG, "App Open Ad is now showing");
                        runOnUiThread(() -> {
                            hideLoadingScreen();
                            isAdShowing = true;
                        });
                    }

                    @Override
                    public void onAdDismissed() {
                        Log.d(TAG, "App Open Ad was dismissed by user");
                        // This will be handled by onShowAdComplete callback
                    }

                    @Override
                    public void onAdFailed(String error) {
                        Log.e(TAG, "App Open Ad failed: " + error);
                        // DO NOT proceed automatically - keep trying
                        runOnUiThread(() -> {
                            showLoadingScreen("Ad error occurred", "Retrying... Please wait");
                        });

                        // Reset loading state and retry after a delay
                        splashHandler.postDelayed(() -> {
                            Log.d(TAG, "Retrying App Open Ad after error...");
                            MyApplication app = (MyApplication) getApplication();
                            AppOpenAdManager adManager = app.getAppOpenAdManager();
                            if (adManager != null) {
                                adManager.resetLoadingState();
                            }
                            showAppOpenAd();
                        }, 5000); // Retry after 5 seconds
                    }
                }
            );

            // Don't mark as showing immediately - wait for actual ad display
            // isAdShowing will be set in onAdShown callback
        } else {
            Log.w(TAG, "AppOpenAdManager not available");
            hideLoadingScreen();
            isAdLoaded = true;
            isAdShowing = false;
            checkAndNavigate();
        }
    }

    /**
     * Check if both config and ad are loaded, then navigate
     */
    private void checkAndNavigate() {
        if (isConfigLoaded && isAdLoaded && !hasNavigated) {
            // Add minimum splash time
            splashHandler.postDelayed(navigationRunnable, SPLASH_SCREEN_TIMEOUT);
        }
    }

    /**
     * Navigate to the next activity
     */
    private void navigateToNextActivity() {
        if (hasNavigated) return;
        hasNavigated = true;

        Log.d(TAG, "Navigating to next activity");

        // Check if it's the user's first time opening the app
        SharedPreferences sharedPreferences = getSharedPreferences(Config.PREFS_APP_SETTINGS, MODE_PRIVATE);
        boolean isFirstTime = sharedPreferences.getBoolean("isFirstTime", true);

        Intent intent;
        if (isFirstTime) {
            // If first time, start Navigation_Activity (onboarding)
            intent = new Intent(Splash_Screen.this, Navigation_Activity.class);
            Log.d(TAG, "First time user, navigating to onboarding");
        } else {
            // If not first time, start MainActivity
            intent = new Intent(Splash_Screen.this, MainActivity.class);
            Log.d(TAG, "Returning user, navigating to main activity");
        }

        startActivity(intent);
        finish();
    }

    /**
     * Show maintenance dialog
     */
    private void showMaintenanceDialog(String message) {
        String displayMessage = message.isEmpty() ?
            "The app is currently under maintenance. Please try again later." : message;

        Toast.makeText(this, displayMessage, Toast.LENGTH_LONG).show();

        // Close app after showing maintenance message
        splashHandler.postDelayed(() -> finish(), 3000);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (splashHandler != null) {
            splashHandler.removeCallbacks(navigationRunnable);
        }
    }
}
