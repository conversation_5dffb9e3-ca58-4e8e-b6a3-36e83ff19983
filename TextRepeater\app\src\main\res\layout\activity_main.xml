<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity"
    android:background="@color/mother_layout_color"
    android:orientation="vertical"
    android:id="@+id/main"
    >

    <!-- Loading Screen Overlay for Ads -->
    <FrameLayout
        android:id="@+id/loadingScreenOverlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#CC000000"
        android:visibility="gone"
        android:elevation="100dp"
        android:clickable="true"
        android:focusable="true">

        <LinearLayout
            android:id="@+id/loadingContainer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="32dp"
            android:background="@drawable/loading_background">

            <!-- Animated Progress Container -->
            <RelativeLayout
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_marginBottom="24dp">

                <!-- Outer Progress Ring -->
                <ProgressBar
                    android:id="@+id/loadingProgressBar"
                    style="?android:attr/progressBarStyleLarge"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:layout_centerInParent="true"
                    android:indeterminateTint="@android:color/white"
                    android:indeterminateTintMode="src_atop" />

                <!-- Inner Pulsing Dot -->
                <View
                    android:id="@+id/pulsingDot"
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:layout_centerInParent="true"
                    android:background="@drawable/pulsing_dot"
                    android:alpha="0.8" />

            </RelativeLayout>

            <!-- Loading Text with Animation -->
            <TextView
                android:id="@+id/loadingText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Loading ads..."
                android:textColor="@android:color/white"
                android:textSize="18sp"
                android:fontFamily="sans-serif-medium"
                android:alpha="0.95"
                android:layout_marginBottom="8dp"
                android:textAlignment="center" />

            <!-- Loading Sub Text -->
            <TextView
                android:id="@+id/loadingSubText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Please wait while we prepare your experience"
                android:textColor="@android:color/white"
                android:textSize="13sp"
                android:layout_marginTop="4dp"
                android:alpha="0.75"
                android:gravity="center"
                android:textAlignment="center"
                android:maxWidth="280dp" />

            <!-- Progress Percentage (Optional) -->
            <TextView
                android:id="@+id/loadingPercentage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text=""
                android:textColor="@android:color/white"
                android:textSize="11sp"
                android:layout_marginTop="16dp"
                android:alpha="0.6"
                android:visibility="gone"
                android:textAlignment="center" />

        </LinearLayout>

    </FrameLayout>


    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBar"
        android:layout_width="match_parent"
        android:layout_height="310dp"
        android:theme="@style/Widget.Design.CollapsingToolbar"
        >
        
        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fitsSystemWindows="true"
            app:contentScrim="@color/mother_layout_color"
            app:layout_scrollFlags="scroll|snap|exitUntilCollapsed"
            app:title="Text Repeater"
            app:expandedTitleTextAppearance="@style/ExpandedTextAppearance"
            app:collapsedTitleTextAppearance="@style/CollapsedTextAppearance"
            android:background="@color/mother_layout_color"
            >


            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="60dp"
                android:layout_marginEnd="65dp"
                android:fontFamily="@font/allerta"
                android:gravity="start"
                android:lineSpacingExtra="5dp"
                android:text="@string/main_title"
                android:textColor="@color/primary_text"
                android:textSize="20sp"
                android:textStyle="bold"
                app:layout_collapseMode="parallax"
                />

            <TextView
                android:id="@+id/textSeeAll"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="60dp"
                android:gravity="start"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                android:layout_gravity="center_vertical"
                android:lineSpacingExtra="6dp"
                android:text="See all messages"
                android:textColor="@color/love"
                android:textSize="16sp"
                android:layout_marginBottom="18dp"
                app:layout_collapseMode="parallax"
                android:drawableEnd="@drawable/baseline_keyboard_arrow_right_24"
                />


            <ImageView
                android:layout_width="150dp"
                android:layout_height="140dp"
                android:layout_gravity="center|end"
                android:layout_marginTop="38dp"
                android:layout_marginRight="32dp"
                android:src="@drawable/message_image"
                android:scaleType="centerCrop"
                app:layout_collapseMode="parallax"
                />

            <androidx.appcompat.widget.Toolbar
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                app:layout_collapseMode="pin"
                />



        </com.google.android.material.appbar.CollapsingToolbarLayout>



    </com.google.android.material.appbar.AppBarLayout>


    <androidx.core.widget.NestedScrollView
        android:id="@+id/constraintLayout9"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/constant_back"
        app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior"
        >


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.0"
            >


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_Text_Repeat"
                android:layout_width="match_parent"
                android:layout_height="125dp"
                android:layout_marginStart="16dp"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="16dp"
                android:background="@drawable/constant_second_back"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.0"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                >


                <ImageView
                    android:id="@+id/imageText"
                    android:layout_width="80dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="8dp"
                    android:layout_marginBottom="24dp"
                    android:layout_marginTop="24dp"
                    android:src="@drawable/repeater_image"
                    android:scaleType="centerCrop"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/textView2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="26dp"
                    android:fontFamily="@font/allerta"
                    android:text="@string/item_title1"
                    android:textColor="@color/primary_text"
                    android:textSize="19sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toEndOf="@+id/imageText"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.0"
                    />


                <TextView
                    android:id="@+id/textView3"
                    android:layout_width="190dp"
                    android:layout_height="wrap_content"
                    android:text="@string/item_sub_title1"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:textColor="@color/secondary_text"
                    android:maxLines="2"
                    android:lineSpacingExtra="2dp"
                    android:fontFamily="@font/alice"
                    android:textSize="15sp"
                    android:layout_marginEnd="55dp"
                    android:ellipsize="end"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toEndOf="@+id/imageText"
                    app:layout_constraintTop_toBottomOf="@+id/textView2"
                    app:layout_constraintVertical_bias="0.0"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="4.5dp"
                    />

                <ImageView
                    android:id="@+id/imageView5"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="1.0"
                    app:layout_constraintStart_toEndOf="@+id/textView3"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/baseline_arrow_forward_ios_24"
                    android:layout_marginEnd="18dp"
                    />


            </androidx.constraintlayout.widget.ConstraintLayout>




            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_message"
                android:layout_width="match_parent"
                android:layout_height="125dp"
                android:layout_marginStart="16dp"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="16dp"
                android:background="@drawable/constant_second_back"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layout_Text_Repeat"
                app:layout_constraintVertical_bias="0.0"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                >


                <ImageView
                    android:id="@+id/imageText1"
                    android:layout_width="80dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="8dp"
                    android:layout_marginTop="24dp"
                    android:layout_marginBottom="24dp"
                    android:scaleType="centerCrop"
                    android:src="@drawable/message_image"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.027"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="1.0" />

                <TextView
                    android:id="@+id/textView24"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="26dp"
                    android:fontFamily="@font/allerta"
                    android:text="@string/short_message"
                    android:textColor="@color/primary_text"
                    android:textSize="19sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toEndOf="@+id/imageText1"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.0"
                    />


                <TextView
                    android:id="@+id/textView37"
                    android:layout_width="190dp"
                    android:layout_height="wrap_content"
                    android:text="@string/messages_des"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:textColor="@color/secondary_text"
                    android:maxLines="2"
                    android:lineSpacingExtra="2dp"
                    android:fontFamily="@font/alice"
                    android:textSize="15sp"
                    android:layout_marginEnd="55dp"
                    android:ellipsize="end"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toEndOf="@+id/imageText1"
                    app:layout_constraintTop_toBottomOf="@+id/textView24"
                    app:layout_constraintVertical_bias="0.0"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="4.5dp"
                    />

                <ImageView
                    android:id="@+id/imageView54"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="1.0"
                    app:layout_constraintStart_toEndOf="@+id/textView37"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/baseline_arrow_forward_ios_24"
                    android:layout_marginEnd="18dp"
                    />


            </androidx.constraintlayout.widget.ConstraintLayout>




            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_Text_To_Imoji"
                android:layout_width="match_parent"
                android:layout_height="125dp"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:background="@drawable/constant_second_back"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layout_message"
                app:layout_constraintVertical_bias="0.0"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                >


                <ImageView
                    android:id="@+id/imageTextToEmoji"
                    android:layout_width="80dp"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop"
                    android:layout_marginTop="24dp"
                    android:layout_marginStart="8dp"
                    android:layout_marginBottom="24dp"
                    android:src="@drawable/text_to_emoji"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.0" />

                <TextView
                    android:id="@+id/textView09"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="26dp"
                    android:fontFamily="@font/allerta"
                    android:text="@string/item_title2"
                    android:textColor="@color/primary_text"
                    android:textSize="19sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toEndOf="@+id/imageTextToEmoji"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.0"

                    />

                <TextView
                    android:id="@+id/textView63"
                    android:layout_width="190dp"
                    android:layout_height="wrap_content"
                    android:text="@string/item_sub_title2"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:textColor="@color/secondary_text"
                    android:maxLines="2"
                    android:lineSpacingExtra="2dp"
                    android:fontFamily="@font/alice"
                    android:textSize="15sp"
                    android:ellipsize="end"
                    android:layout_marginEnd="55dp"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toEndOf="@+id/imageTextToEmoji"
                    app:layout_constraintTop_toBottomOf="@+id/textView09"
                    app:layout_constraintVertical_bias="0.0"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="4.5dp"
                    />

                <ImageView
                    android:id="@+id/imageView40"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="1.0"
                    app:layout_constraintStart_toEndOf="@+id/textView63"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/baseline_arrow_forward_ios_24"
                    android:layout_marginEnd="18dp"
                    />



            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_Stylish_Font"
                android:layout_width="match_parent"
                android:layout_height="125dp"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:background="@drawable/constant_second_back"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layout_Text_To_Imoji"
                app:layout_constraintVertical_bias="0.0"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"

                >



                <ImageView
                    android:id="@+id/imageStylish_Font"
                    android:layout_width="80dp"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop"
                    android:layout_marginTop="24dp"
                    android:layout_marginStart="8dp"
                    android:layout_marginBottom="24dp"
                    android:src="@drawable/image_stylish"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.0" />





                <TextView
                    android:id="@+id/textView001"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="26dp"
                    android:fontFamily="@font/allerta"
                    android:text="@string/item_title3"
                    android:textColor="@color/primary_text"
                    android:textSize="19sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toEndOf="@+id/imageStylish_Font"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.0"

                    />

                <TextView
                    android:id="@+id/textView64"
                    android:layout_width="190dp"
                    android:layout_height="wrap_content"
                    android:text="@string/item_sub_title3"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:textColor="@color/secondary_text"
                    android:maxLines="2"
                    android:lineSpacingExtra="2dp"
                    android:fontFamily="@font/alice"
                    android:textSize="15sp"
                    android:ellipsize="end"
                    android:layout_marginEnd="55dp"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toEndOf="@+id/imageStylish_Font"
                    app:layout_constraintTop_toBottomOf="@+id/textView001"
                    app:layout_constraintVertical_bias="0.0"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="4.5dp"
                    />

                <ImageView
                    android:id="@+id/imageView47"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="1.0"
                    app:layout_constraintStart_toEndOf="@+id/textView64"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/baseline_arrow_forward_ios_24"
                    android:layout_marginEnd="18dp"
                    />






            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_Stylish_Number"
                android:layout_width="match_parent"
                android:layout_height="125dp"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:background="@drawable/constant_second_back"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layout_Stylish_Font"
                app:layout_constraintVertical_bias="0.0"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                >




                <ImageView
                    android:id="@+id/imageEmojiArt"
                    android:layout_width="80dp"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop"
                    android:layout_marginTop="24dp"
                    android:layout_marginStart="8dp"
                    android:layout_marginBottom="24dp"
                    android:src="@drawable/image_art"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.0" />




                <TextView
                    android:id="@+id/textView081"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="26dp"
                    android:fontFamily="@font/allerta"
                    android:text="@string/item_title4"
                    android:textColor="@color/primary_text"
                    android:textSize="19sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toEndOf="@+id/imageEmojiArt"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.0"

                    />

                <TextView
                    android:id="@+id/textView84"
                    android:layout_width="190dp"
                    android:layout_height="wrap_content"
                    android:text="@string/item_sub_title4"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:textColor="@color/secondary_text"
                    android:maxLines="2"
                    android:lineSpacingExtra="2dp"
                    android:fontFamily="@font/alice"
                    android:textSize="15sp"
                    android:ellipsize="end"
                    android:layout_marginEnd="55dp"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toEndOf="@+id/imageEmojiArt"
                    app:layout_constraintTop_toBottomOf="@+id/textView081"
                    app:layout_constraintVertical_bias="0.0"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="4.5dp"
                    />

                <ImageView
                    android:id="@+id/imageView07"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="1.0"
                    app:layout_constraintStart_toEndOf="@+id/textView84"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/baseline_arrow_forward_ios_24"
                    android:layout_marginEnd="18dp"
                    />


            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_Decoration_Text"
                android:layout_width="match_parent"
                android:layout_height="125dp"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:background="@drawable/constant_second_back"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layout_Stylish_Number"
                app:layout_constraintVertical_bias="0.0"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                >



                <ImageView
                    android:id="@+id/imageDecoration"
                    android:layout_width="80dp"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop"
                    android:layout_marginTop="24dp"
                    android:layout_marginStart="8dp"
                    android:layout_marginBottom="24dp"
                    android:src="@drawable/decoration_image"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.0" />





                <TextView
                    android:id="@+id/textView071"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="26dp"
                    android:fontFamily="@font/allerta"
                    android:text="@string/item_title5"
                    android:textColor="@color/primary_text"
                    android:textSize="19sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toEndOf="@+id/imageDecoration"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.0"

                    />

                <TextView
                    android:id="@+id/textView14"
                    android:layout_width="190dp"
                    android:layout_height="wrap_content"
                    android:text="@string/item_sub_title5"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:textColor="@color/secondary_text"
                    android:maxLines="2"
                    android:lineSpacingExtra="2dp"
                    android:fontFamily="@font/alice"
                    android:textSize="15sp"
                    android:ellipsize="end"
                    android:layout_marginEnd="55dp"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toEndOf="@+id/imageDecoration"
                    app:layout_constraintTop_toBottomOf="@+id/textView071"
                    app:layout_constraintVertical_bias="0.0"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="4.5dp"
                    />

                <ImageView
                    android:id="@+id/imageView417"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="1.0"
                    app:layout_constraintStart_toEndOf="@+id/textView14"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/baseline_arrow_forward_ios_24"
                    android:layout_marginEnd="18dp"
                    />


            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_Random_Text"
                android:layout_width="match_parent"
                android:layout_height="125dp"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:background="@drawable/constant_second_back"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layout_Decoration_Text"
                app:layout_constraintVertical_bias="0.0"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                >




                <ImageView
                    android:id="@+id/imageRandom"
                    android:layout_width="80dp"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop"
                    android:layout_marginTop="24dp"
                    android:layout_marginStart="8dp"
                    android:layout_marginBottom="24dp"
                    android:src="@drawable/random_image"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.0" />




                <TextView
                    android:id="@+id/textView037"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="26dp"
                    android:fontFamily="@font/allerta"
                    android:text="@string/item_title6"
                    android:textColor="@color/primary_text"
                    android:textSize="19sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toEndOf="@+id/imageRandom"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.0"

                    />

                <TextView
                    android:id="@+id/textView014"
                    android:layout_width="190dp"
                    android:layout_height="wrap_content"
                    android:text="@string/item_sub_title6"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:textColor="@color/secondary_text"
                    android:maxLines="2"
                    android:lineSpacingExtra="2dp"
                    android:fontFamily="@font/alice"
                    android:textSize="15sp"
                    android:ellipsize="end"
                    android:layout_marginEnd="55dp"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toEndOf="@+id/imageRandom"
                    app:layout_constraintTop_toBottomOf="@+id/textView037"
                    app:layout_constraintVertical_bias="0.0"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="4.5dp"
                    />

                <ImageView
                    android:id="@+id/imageView60"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="1.0"
                    app:layout_constraintStart_toEndOf="@+id/textView014"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/baseline_arrow_forward_ios_24"
                    android:layout_marginEnd="18dp"
                    />


            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_Blank_Text"
                android:layout_width="match_parent"
                android:layout_height="125dp"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:background="@drawable/constant_second_back"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layout_Random_Text"
                app:layout_constraintVertical_bias="0.0"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                android:layout_marginBottom="100dp"
                >



                <ImageView
                    android:id="@+id/imageBlank"
                    android:layout_width="80dp"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop"
                    android:layout_marginTop="24dp"
                    android:layout_marginStart="8dp"
                    android:layout_marginBottom="24dp"
                    android:src="@drawable/blank_image"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.0" />



                <TextView
                    android:id="@+id/textView031"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="26dp"
                    android:fontFamily="@font/allerta"
                    android:text="@string/item_title7"
                    android:textColor="@color/primary_text"
                    android:textSize="19sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toEndOf="@+id/imageBlank"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.0"

                    />

                <TextView
                    android:id="@+id/textView017"
                    android:layout_width="190dp"
                    android:layout_height="wrap_content"
                    android:text="@string/item_sub_title7"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:textColor="@color/secondary_text"
                    android:maxLines="2"
                    android:lineSpacingExtra="2dp"
                    android:fontFamily="@font/alice"
                    android:textSize="15sp"
                    android:ellipsize="end"
                    android:layout_marginEnd="55dp"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toEndOf="@+id/imageBlank"
                    app:layout_constraintTop_toBottomOf="@+id/textView031"
                    app:layout_constraintVertical_bias="0.0"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="4.5dp"
                    />

                <ImageView
                    android:id="@+id/imageView67"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="1.0"
                    app:layout_constraintStart_toEndOf="@+id/textView017"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/baseline_arrow_forward_ios_24"
                    android:layout_marginEnd="18dp"
                    />


            </androidx.constraintlayout.widget.ConstraintLayout>


        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.core.widget.NestedScrollView>



    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:baselineAlignBottom="false"
        android:clickable="true"
        app:layout_anchor="@id/appBar"
        app:layout_anchorGravity="end"
        android:layout_marginRight="16dp"
        android:layout_marginTop="8dp"
        android:src="@drawable/baseline_settings_24"
        android:backgroundTint="@color/sub_layout_color"
        app:elevation="0dp"
        app:rippleColor="@color/love"
        app:fabSize="normal"
        android:elevation="0dp"
        app:shapeAppearanceOverlay="@style/ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton"
        >





    </com.google.android.material.floatingactionbutton.FloatingActionButton>



    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:paddingTop="6dp"
        android:paddingBottom="5dp"
        android:background="@color/sub_layout_color"
        >


        <FrameLayout
            android:id="@+id/ad_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_margin="5dp"
            />



    </RelativeLayout>









</androidx.coordinatorlayout.widget.CoordinatorLayout>