package com.anginatech.textrepeater;



import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import android.content.Intent;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowInsetsController;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.google.android.gms.ads.AdError;
import com.google.android.gms.ads.FullScreenContentCallback;
import com.google.android.material.floatingactionbutton.FloatingActionButton;


public class MainActivity extends AppCompatActivity {

    TextView textSeeAll;
    private static final int UPDATE_REQUEST_CODE = 22;
    private InAppUpdate inAppUpdate;
    private ViewGroup adContainer;

    // Click debouncing variables
    private static final long CLICK_DEBOUNCE_TIME = 1000; // 1 second
    private long lastClickTime = 0;
    private boolean isNavigating = false;
    private Handler mainHandler;

    // Loading screen variables
    private FrameLayout loadingScreenOverlay;
    private LinearLayout loadingContainer;
    private ProgressBar loadingProgressBar;
    private TextView loadingText;
    private TextView loadingSubText;
    private TextView loadingPercentage;
    private View pulsingDot;

    // Loading state tracking
    private boolean isLoadingScreenVisible = false;
    private boolean isBannerAdLoaded = false;
    private boolean isInterstitialAdLoaded = false;
    private boolean isAdsConfigLoaded = false;
    private long loadingStartTime;
    private static final long MAX_LOADING_TIMEOUT = 7000; // 7 seconds maximum
    private final String[] loadingMessages = {
        "Initializing ads...",
        "Loading configuration...",
        "Preparing banner ads...",
        "Loading interstitial ads...",
        "Almost ready..."
    };

    // Activity navigation enum for better type safety
    private enum NavigationTarget {
        SETTINGS(Settings_Activity.class),
        TEXT_REPEAT(Text_Repeat.class),
        MESSAGE(Message_Activity.class),
        TEXT_TO_EMOJI(Text_to_Imoji_Activity.class),
        STYLISH_FONT(Stylish_Font_Activity.class),
        EMOJI_ART(Emoji_Art.class),
        DECORATION_TEXT(Decoration_Text_Activity.class),
        RANDOM_TEXT(Random_Text_Activity.class),
        BLANK_TEXT(Blank_Text_Activity.class);

        private final Class<?> activityClass;

        NavigationTarget(Class<?> activityClass) {
            this.activityClass = activityClass;
        }

        public Class<?> getActivityClass() {
            return activityClass;
        }
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_main);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        // Apply night mode setting immediately for smooth UI
        applyNightModeSettings();

        // Initialize critical UI components first for immediate user interaction
        initializeUIComponents();

        // Initialize ads asynchronously to prevent UI blocking
        initializeAdsAsync();

        ///Initialized the element=====================================================================

        textSeeAll = findViewById(R.id.textSeeAll);
        FloatingActionButton fab = findViewById(R.id.fab);
        fab.setSize(FloatingActionButton.SIZE_NORMAL);

        ConstraintLayout layout_Text_Repeat = findViewById(R.id.layout_Text_Repeat);
        ConstraintLayout layout_message = findViewById(R.id.layout_message);
        ConstraintLayout layout_Text_To_Imoji = findViewById(R.id.layout_Text_To_Imoji);
        ConstraintLayout layout_Stylish_Font = findViewById(R.id.layout_Stylish_Font);
        ConstraintLayout layout_Stylish_Number = findViewById(R.id.layout_Stylish_Number);
        ConstraintLayout layout_Decoration_Text = findViewById(R.id.layout_Decoration_Text);
        ConstraintLayout layout_Random_Text = findViewById(R.id.layout_Random_Text);
        ConstraintLayout layout_Blank_Text = findViewById(R.id.layout_Blank_Text);



        ///Initialized the element=====================================================================


        inAppUpdate = new InAppUpdate(MainActivity.this);
        inAppUpdate.checkForAppUpdate();

        // Initialize handler for smooth UI operations
        mainHandler = new Handler(Looper.getMainLooper());



        textSeeAll.setOnClickListener(createOptimizedClickListener(v ->
            navigateToActivity(Message_Activity.class)
        ));

        fab.setOnClickListener(createOptimizedClickListener(v ->
            navigateWithAd(Settings_Activity.class)
        ));

        layout_Text_Repeat.setOnClickListener(createOptimizedClickListener(v ->
            navigateWithAd(Text_Repeat.class)
        ));

        layout_message.setOnClickListener(createOptimizedClickListener(v ->
            navigateWithAd(Message_Activity.class)
        ));

        layout_Text_To_Imoji.setOnClickListener(createOptimizedClickListener(v ->
            navigateWithAd(Text_to_Imoji_Activity.class)
        ));

        layout_Stylish_Font.setOnClickListener(createOptimizedClickListener(v ->
            navigateWithAd(Stylish_Font_Activity.class)
        ));

        layout_Stylish_Number.setOnClickListener(createOptimizedClickListener(v ->
            navigateWithAd(Emoji_Art.class)
        ));

        layout_Decoration_Text.setOnClickListener(createOptimizedClickListener(v ->
            navigateWithAd(Decoration_Text_Activity.class)
        ));

        layout_Random_Text.setOnClickListener(createOptimizedClickListener(v ->
            navigateWithAd(Random_Text_Activity.class)
        ));

        layout_Blank_Text.setOnClickListener(createOptimizedClickListener(v ->
            navigateWithAd(Blank_Text_Activity.class)
        ));

    }

    /**
     * Apply night mode settings immediately for smooth UI
     */
    private void applyNightModeSettings() {
        SharedPreferences sharedPreferences = getSharedPreferences("nightModePrefs", MODE_PRIVATE);
        boolean isNightMode = sharedPreferences.getBoolean("nightMode", false);

        if (isNightMode) {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES);
        } else {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);
        }
    }

    /**
     * Initialize critical UI components first for immediate user interaction
     */
    private void initializeUIComponents() {
        // Initialize views immediately for user interaction
        adContainer = findViewById(R.id.ad_container);

        // Initialize loading screen components
        initializeLoadingScreen();

        // Show loading placeholder for ad container
        showAdLoadingPlaceholder();

        // Initialize other critical components
        inAppUpdate = new InAppUpdate(MainActivity.this);
        inAppUpdate.checkForAppUpdate();

        // Initialize handler for smooth UI operations
        mainHandler = new Handler(Looper.getMainLooper());

        // Handle notification click tracking
        handleNotificationClick();
    }

    /**
     * Initialize loading screen UI elements
     */
    private void initializeLoadingScreen() {
        try {
            loadingScreenOverlay = findViewById(R.id.loadingScreenOverlay);
            loadingContainer = findViewById(R.id.loadingContainer);
            loadingProgressBar = findViewById(R.id.loadingProgressBar);
            loadingText = findViewById(R.id.loadingText);
            loadingSubText = findViewById(R.id.loadingSubText);
            loadingPercentage = findViewById(R.id.loadingPercentage);
            pulsingDot = findViewById(R.id.pulsingDot);

            // Start pulsing animation for the dot
            if (pulsingDot != null) {
                Animation pulseAnimation = AnimationUtils.loadAnimation(this, R.anim.pulse_animation);
                pulsingDot.startAnimation(pulseAnimation);
            }
        } catch (Exception e) {
            // Handle gracefully if UI elements are not found
            Log.w("MainActivity", "Loading screen UI elements not found: " + e.getMessage());
        }
    }

    /**
     * Initialize ads asynchronously to prevent UI blocking
     */
    private void initializeAdsAsync() {
        // Check if ads are enabled before showing loading screen
        SharedPreferences prefs = getSharedPreferences("ads_config", MODE_PRIVATE);
        boolean adsEnabled = prefs.getBoolean("is_ads_enabled", true); // Default to true

        if (adsEnabled) {
            // Show loading screen for ads
            showLoadingScreen("Initializing ads...", "Please wait while we prepare your experience");
            loadingStartTime = System.currentTimeMillis();

            // Set maximum timeout for loading screen
            mainHandler.postDelayed(() -> {
                if (isLoadingScreenVisible) {
                    Log.w("MainActivity", "Loading screen timeout reached, hiding loading screen");
                    hideLoadingScreen();
                }
            }, MAX_LOADING_TIMEOUT);
        }

        // Use background thread for ad initialization to prevent UI blocking
        new Thread(() -> {
            try {
                // Initialize consent form on background thread
                runOnUiThread(() -> initMobileAds.requestConsentForm(MainActivity.this));

                // Small delay to ensure consent is processed
                Thread.sleep(100);

                // Initialize ads configuration
                runOnUiThread(() -> {
                    if (adsEnabled) {
                        updateLoadingMessage(1); // "Loading configuration..."
                    }

                    AdsHelper.initializeAds(MainActivity.this);
                    isAdsConfigLoaded = true;

                    // Load ads with delay to prevent blocking
                    mainHandler.postDelayed(() -> {
                        loadAdsWithLoadingScreen();
                    }, 200); // 200ms delay for smooth UI
                });

            } catch (InterruptedException e) {
                Log.e("MainActivity", "Ad initialization interrupted: " + e.getMessage());
                // Fallback to immediate loading if thread is interrupted
                runOnUiThread(() -> loadAdsWithLoadingScreen());
            }
        }).start();
    }

    /**
     * Load ads with loading screen integration
     */
    private void loadAdsWithLoadingScreen() {
        try {
            if (isLoadingScreenVisible) {
                updateLoadingMessage(2); // "Preparing banner ads..."
            }

            // Load banner ad with timeout
            if (adContainer != null) {
                AdsHelper.loadBannerAd(this, adContainer);

                // Check banner ad loading after timeout
                mainHandler.postDelayed(() -> {
                    isBannerAdLoaded = true;
                    hideAdLoadingPlaceholder();
                    checkAdLoadingComplete();
                }, 1500); // 1.5 seconds timeout for banner ad
            } else {
                isBannerAdLoaded = true;
            }

            // Load interstitial ad asynchronously
            mainHandler.postDelayed(() -> {
                if (isLoadingScreenVisible) {
                    updateLoadingMessage(3); // "Loading interstitial ads..."
                }

                AdsHelper.loadInterstitialAd(this);

                // Check interstitial ad loading after timeout
                mainHandler.postDelayed(() -> {
                    isInterstitialAdLoaded = true;
                    checkAdLoadingComplete();
                }, 2000); // 2 seconds timeout for interstitial ad

            }, 300); // 300ms delay between ad loads

        } catch (Exception e) {
            Log.e("MainActivity", "Error loading ads: " + e.getMessage());
            hideAdLoadingPlaceholder();
            hideLoadingScreen();
        }
    }

    /**
     * Check if all ads have finished loading and hide loading screen
     */
    private void checkAdLoadingComplete() {
        if (isAdsConfigLoaded && isBannerAdLoaded && isInterstitialAdLoaded) {
            if (isLoadingScreenVisible) {
                updateLoadingMessage(4); // "Almost ready..."

                // Small delay before hiding to show "Almost ready" message
                mainHandler.postDelayed(() -> {
                    hideLoadingScreen();
                }, 500);
            }
        }
    }

    /**
     * Show loading screen with custom message
     */
    private void showLoadingScreen(String message, String subMessage) {
        try {
            if (loadingScreenOverlay != null) {
                loadingScreenOverlay.setVisibility(View.VISIBLE);
                isLoadingScreenVisible = true;
            }
            if (loadingText != null) {
                loadingText.setText(message);
            }
            if (loadingSubText != null) {
                loadingSubText.setText(subMessage);
            }
        } catch (Exception e) {
            Log.w("MainActivity", "Error showing loading screen: " + e.getMessage());
        }
    }

    /**
     * Hide loading screen
     */
    private void hideLoadingScreen() {
        try {
            if (loadingScreenOverlay != null) {
                loadingScreenOverlay.setVisibility(View.GONE);
                isLoadingScreenVisible = false;
            }
        } catch (Exception e) {
            Log.w("MainActivity", "Error hiding loading screen: " + e.getMessage());
        }
    }

    /**
     * Update loading message with step progression
     */
    private void updateLoadingMessage(int step) {
        if (step < loadingMessages.length && isLoadingScreenVisible) {
            showLoadingScreen(loadingMessages[step], "Please wait while we prepare your experience");

            // Update progress percentage if available
            if (loadingPercentage != null) {
                int percentage = (step + 1) * 100 / loadingMessages.length;
                loadingPercentage.setText(percentage + "%");
                loadingPercentage.setVisibility(View.VISIBLE);
            }
        }
    }

    /**
     * Show loading placeholder for ad container
     */
    private void showAdLoadingPlaceholder() {
        if (adContainer != null) {
            // Create a subtle loading indicator
            adContainer.removeAllViews();

            // Add a minimal height placeholder to prevent layout shifts
            adContainer.setMinimumHeight(50); // Minimal height for banner ad
            adContainer.setBackgroundColor(getResources().getColor(android.R.color.transparent));
        }
    }

    /**
     * Hide loading placeholder for ad container
     */
    private void hideAdLoadingPlaceholder() {
        if (adContainer != null) {
            // Reset container properties
            adContainer.setMinimumHeight(0);
            adContainer.setBackgroundColor(getResources().getColor(android.R.color.transparent));
        }
    }

    /**
     * Create optimized click listener with immediate visual feedback and debouncing
     */
    private View.OnClickListener createOptimizedClickListener(View.OnClickListener action) {
        return new View.OnClickListener() {
            private long lastClickTime = 0;
            private static final long DEBOUNCE_TIME = 500; // 500ms debounce

            @Override
            public void onClick(View v) {
                long currentTime = System.currentTimeMillis();

                // Debounce rapid clicks
                if (currentTime - lastClickTime < DEBOUNCE_TIME) {
                    return;
                }
                lastClickTime = currentTime;

                // Immediate visual feedback
                v.setAlpha(0.7f);
                v.animate().alpha(1.0f).setDuration(150);

                // Execute action asynchronously to prevent UI blocking
                mainHandler.post(() -> {
                    try {
                        action.onClick(v);
                    } catch (Exception e) {
                        Log.e("MainActivity", "Error in click action: " + e.getMessage());
                    }
                });
            }
        };
    }

    /**
     * Navigate with ad in an optimized way
     */
    private void navigateWithAd(Class<?> targetActivity) {
        if (AdsHelper.isInterstitialAdLoaded()) {
            AdsHelper.showInterstitialAd(MainActivity.this, new FullScreenContentCallback() {
                @Override
                public void onAdDismissedFullScreenContent() {
                    // Load the next interstitial ad asynchronously
                    new Thread(() -> AdsHelper.loadInterstitialAd(MainActivity.this)).start();

                    // Navigate to target activity
                    navigateToActivity(targetActivity);
                }

                @Override
                public void onAdFailedToShowFullScreenContent(AdError adError) {
                    // Ad failed to show, just navigate to target activity
                    navigateToActivity(targetActivity);
                }
            });
        } else {
            // If ad not loaded, just navigate to target activity
            navigateToActivity(targetActivity);
            // Load the next interstitial ad for future use
            new Thread(() -> AdsHelper.loadInterstitialAd(MainActivity.this)).start();
        }
    }

    /**
     * Navigate to activity with smooth transition
     */
    private void navigateToActivity(Class<?> targetActivity) {
        try {
            Intent intent = new Intent(MainActivity.this, targetActivity);
            startActivity(intent);

            // Add smooth transition animation
            overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out);

            finish();
        } catch (Exception e) {
            Log.e("MainActivity", "Error navigating to activity: " + e.getMessage());
        }
    }

    /**
     * Handle notification click tracking
     */
    private void handleNotificationClick() {
        Intent intent = getIntent();
        if (intent != null && intent.getBooleanExtra("opened_from_notification", false)) {
            String notificationId = intent.getStringExtra("notification_id");
            if (notificationId != null && !notificationId.isEmpty()) {
                MyFirebaseMessagingService.trackNotificationClick(this, notificationId);
            }
        }
    }



    @Override
    protected void onResume() {
        super.onResume();
        inAppUpdate.onResume();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        inAppUpdate.onDestroy();
    }







}